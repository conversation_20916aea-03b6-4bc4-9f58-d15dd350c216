package com.illumio.data.configuration;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "ip-classification-mmdb")
@Data
public class IpClassificationMmdbConfig {
    private final KafkaCommonConfig kafkaCommonConfig;
    private final KafkaReceiverConfig kafkaReceiverConfig;
    private final KafkaSenderConfig kafkaSenderConfig;
    private final BlueMmdbClientConfig blueMmdbClientConfig;
    private final GrpcConfig grpcConfig;
    private final BackpressureConfig backpressureConfig;

    public Map<String, Object> createConsumerProps() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(
            ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
            this.getKafkaCommonConfig().getBootstrapServers());
        consumerProps.put(
            ConsumerConfig.GROUP_ID_CONFIG,
            this.getKafkaReceiverConfig().getGroupId());
        if (null != this.getKafkaReceiverConfig().getGroupInstanceId()) {
            consumerProps.put(ConsumerConfig.GROUP_INSTANCE_ID_CONFIG,
                this.getKafkaReceiverConfig().getGroupInstanceId());
        }
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(
            ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG,
            this.getKafkaReceiverConfig().getRequestTimeoutMs());
        consumerProps.put(
            ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
            this.getKafkaReceiverConfig().getAutoOffsetReset());
        if (null != this.getKafkaCommonConfig().getIsSasl()
            && this.getKafkaCommonConfig().getIsSasl()) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(
                SaslConfigs.SASL_JAAS_CONFIG,
                this.getKafkaCommonConfig().getSaslJaasConfig());
        }
        consumerProps.put(
            ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
            this.getKafkaReceiverConfig().getMaxPollRecords());
        consumerProps.put(
            ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
            this.getKafkaReceiverConfig().getMaxPartitionFetchBytes());
        consumerProps.put(ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG,
            this.getKafkaReceiverConfig().getAssignmentStrategy());
        // Maximum time between poll() calls before a rebalance is triggered
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG,
            this.getKafkaReceiverConfig().getMaxPollIntervalMs());
        // Maximum time the coordinator waits for a heartbeat before marking the consumer as dead
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,
            this.getKafkaReceiverConfig().getSessionTimeoutMs());
        // Frequency of heartbeats to keep the consumer's session active
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG,
            this.getKafkaReceiverConfig().getHeartbeatIntervalMs());
        consumerProps.put(CommonClientConfigs.METADATA_MAX_AGE_CONFIG,
            this.getKafkaCommonConfig().getMetadataMaxAgeMs());
        consumerProps.put(CommonClientConfigs.CONNECTIONS_MAX_IDLE_MS_CONFIG,
            this.getKafkaCommonConfig().getConnectionMaxIdleMs());

        // Reactor Kafka specific configurations for better rebalancing handling
        consumerProps.put("reactor.kafka.commit-retries", this.getKafkaReceiverConfig().getMaxRetries());
        consumerProps.put("reactor.kafka.commit-retry-interval", Duration.ofMillis(this.getKafkaReceiverConfig().getCommitRetryIntervalMs()));

        // Rebalance timeout configuration
        consumerProps.put(ConsumerConfig.REBALANCE_TIMEOUT_MS_CONFIG, this.getKafkaReceiverConfig().getRebalanceTimeoutMs());

        // Additional consumer configurations for better rebalancing
        consumerProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false); // Ensure manual commit control
        consumerProps.put(ConsumerConfig.ISOLATION_LEVEL_CONFIG, "read_committed");

        return consumerProps;
    }

    @Configuration
    @Getter
    @Setter
    public static class BackpressureConfig {
        private boolean enabled = false;
        private int maxSize = 400;
        private Duration maxTime = Duration.ofMillis(500);
        private int lowTide = 60;
        private int highTide = 600;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaCommonConfig {
        private String bootstrapServers;
        private Boolean isSasl = false;
        private String saslJaasConfig;
        private Integer metadataMaxAgeMs;
        private Integer connectionMaxIdleMs;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaReceiverConfig {
        private String topic;
        private String groupId;
        // Currently populated as env variable IPCLASSIFICATIONMMDB_KAFKARECEIVERCONFIG_GROUPINSTANCEID
        private String groupInstanceId;
        private String autoOffsetReset;
        private Integer requestTimeoutMs;
        private Integer maxPollRecords;
        private Integer maxPartitionFetchBytes;
        private int maxRetries = 5;
        private Duration maxBackoff = Duration.ofSeconds(3);
        private String assignmentStrategy = "org.apache.kafka.clients.consumer.CooperativeStickyAssignor";
        private Integer maxPollIntervalMs = 300000;
        private Integer sessionTimeoutMs = 300000;
        private Integer heartbeatIntervalMs = 3000;
        private Integer monitorIntervalInMs = 30*1000; // 30 seconds
        private Integer noMessagesInMs = 180*1000; // 180 seconds (3 minutes) - reduced from 6 minutes
        // Additional rebalancing configurations
        private Integer rebalanceTimeoutMs = 60000; // 60 seconds for rebalance timeout
        private Integer commitRetryIntervalMs = 500; // 500ms between commit retries
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaSenderConfig {
        private String sinkTopic;
        private Integer maxRequestSize;
        private Integer requestTimeoutMs;
        private Integer metadataMaxIdleMs;
        private Integer deliveryTimeoutMs;
        private int maxRetries = 5;
        private Duration maxBackoff = Duration.ofSeconds(3);
        private Integer batchSizeKb;
        private Long bufferMemoryMb;
        private Long lingerMs;
        private int numProducers = 1;
    }

    @Configuration
    @Getter
    @Setter
    public static class GrpcConfig {
        private Duration timeout = Duration.ofSeconds(1);
        private int maxRetries = 3;
        private Duration initialBackoff = Duration.ofMillis(500);
        private Duration maxBackoff = Duration.ofSeconds(1);
    }
}
