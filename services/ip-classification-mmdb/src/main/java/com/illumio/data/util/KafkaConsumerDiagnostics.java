package com.illumio.data.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.kafka.receiver.KafkaReceiver;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
@RestController
public class KafkaConsumerDiagnostics {
    
    private final com.illumio.data.IpClassificationMmdbPipeline pipeline;
    
    public KafkaConsumerDiagnostics(com.illumio.data.IpClassificationMmdbPipeline pipeline) {
        this.pipeline = pipeline;
    }
    
    @GetMapping("/diagnostics/kafka-consumers")
    public Map<String, Object> getKafkaConsumerDiagnostics() {
        Map<String, Object> diagnostics = new HashMap<>();
        
        try {
            // Get current KafkaReceiver instance
            Field currentReceiverField = pipeline.getClass().getDeclaredField("currentKafkaReceiver");
            currentReceiverField.setAccessible(true);
            AtomicReference<KafkaReceiver<String, String>> currentReceiver = 
                (AtomicReference<KafkaReceiver<String, String>>) currentReceiverField.get(pipeline);
            
            KafkaReceiver<String, String> receiver = currentReceiver.get();
            diagnostics.put("currentReceiverInstance", receiver != null ? receiver.toString() : "null");
            diagnostics.put("currentReceiverHashCode", receiver != null ? receiver.hashCode() : "null");
            
            // Check if recreation is in progress
            Field isRecreatingField = pipeline.getClass().getDeclaredField("isRecreating");
            isRecreatingField.setAccessible(true);
            AtomicReference<Boolean> isRecreating = (AtomicReference<Boolean>) isRecreatingField.get(pipeline);
            diagnostics.put("isRecreating", isRecreating.get());
            
            // Get last message time
            Field lastMessageTimeField = pipeline.getClass().getDeclaredField("lastMessageTime");
            lastMessageTimeField.setAccessible(true);
            AtomicReference<Long> lastMessageTime = (AtomicReference<Long>) lastMessageTimeField.get(pipeline);
            long currentTime = System.currentTimeMillis();
            long lastMessage = lastMessageTime.get();
            diagnostics.put("lastMessageTime", lastMessage);
            diagnostics.put("timeSinceLastMessage", currentTime - lastMessage);
            
            // Get disposable status
            Field disposableField = pipeline.getClass().getDeclaredField("disposable");
            disposableField.setAccessible(true);
            Object disposable = disposableField.get(pipeline);
            diagnostics.put("disposableExists", disposable != null);
            if (disposable != null) {
                diagnostics.put("disposableIsDisposed", 
                    ((reactor.core.Disposable) disposable).isDisposed());
            }
            
            // Get observed partitions
            Field observedPartitionsField = pipeline.getClass().getDeclaredField("observedPartitions");
            observedPartitionsField.setAccessible(true);
            Object observedPartitions = observedPartitionsField.get(pipeline);
            if (observedPartitions != null) {
                diagnostics.put("observedPartitionsCount", 
                    ((java.util.concurrent.ConcurrentHashMap) observedPartitions).size());
            }
            
        } catch (Exception e) {
            log.error("Error getting diagnostics", e);
            diagnostics.put("error", e.getMessage());
        }
        
        // Add thread information
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        diagnostics.put("totalThreads", threadBean.getThreadCount());
        diagnostics.put("daemonThreads", threadBean.getDaemonThreadCount());
        
        // Add JVM memory info
        Runtime runtime = Runtime.getRuntime();
        diagnostics.put("totalMemory", runtime.totalMemory());
        diagnostics.put("freeMemory", runtime.freeMemory());
        diagnostics.put("maxMemory", runtime.maxMemory());
        
        return diagnostics;
    }
    
    @GetMapping("/diagnostics/kafka-threads")
    public Map<String, Object> getKafkaThreads() {
        Map<String, Object> threadInfo = new HashMap<>();
        
        ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
        long[] threadIds = threadBean.getAllThreadIds();
        
        int kafkaThreads = 0;
        int reactorThreads = 0;
        int consumerThreads = 0;
        
        for (long threadId : threadIds) {
            try {
                String threadName = threadBean.getThreadInfo(threadId).getThreadName();
                if (threadName.toLowerCase().contains("kafka")) {
                    kafkaThreads++;
                }
                if (threadName.toLowerCase().contains("reactor")) {
                    reactorThreads++;
                }
                if (threadName.toLowerCase().contains("consumer")) {
                    consumerThreads++;
                }
            } catch (Exception e) {
                // Thread might have died, ignore
            }
        }
        
        threadInfo.put("kafkaThreads", kafkaThreads);
        threadInfo.put("reactorThreads", reactorThreads);
        threadInfo.put("consumerThreads", consumerThreads);
        threadInfo.put("totalThreads", threadIds.length);
        
        return threadInfo;
    }
    
    @GetMapping("/diagnostics/force-consumer-recreation")
    public Map<String, Object> forceConsumerRecreation() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // Use reflection to call recreateConsumerAndRestart
            java.lang.reflect.Method recreateMethod = pipeline.getClass()
                .getDeclaredMethod("recreateConsumerAndRestart", String.class);
            recreateMethod.setAccessible(true);
            recreateMethod.invoke(pipeline, "manual_diagnostic_recreation");
            
            result.put("status", "Recreation triggered");
            result.put("reason", "manual_diagnostic_recreation");
            
        } catch (Exception e) {
            log.error("Error forcing recreation", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
