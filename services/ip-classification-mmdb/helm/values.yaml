ports:
  - name: admin
    port: 8084
  - name: rest
    port: 8081

servicePorts:
  - name: rest
    podPort: rest
    servicePort: 8081

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/
  repositoryName: ip-classification-mmdb
  tag:      # value given at helm deployment
  pullPolicy: Always

imagePullSecrets: [ ]
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 6
  targetCPUUtilizationPercentage: 60
  targetMemoryUtilizationPercentage: 60

podAnnotations: { }

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000



resources: { }
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
#   memory: 128Mi

nodeSelector: { }

tolerations: [ ]

affinity: { }

extraLabels: { }

eventhub:
  password:                 # should give at deployment time

applicationYml:
  logging:
    level:
      ROOT: INFO
      org:
        apache:
          kafka: INFO
  spring:
    application:
      name: ip-classification-mmdb
    output:
      ansi:
        enabled: ALWAYS
  ipClassificationMmdb:
    kafkaCommonConfig:
      bootstrapServers: eventhubs-connector.servicebus.windows.net:9093
      isSasl: true
      saslJaasConfig: _DO_NOT_COMMIT_
      metadataMaxAgeMs: 300000      # 5 minutes
      connectionMaxIdleMs: 540000   # 9 minutes - much longer than session timeout to prevent connection drops
    kafkaReceiverConfig:
      topic: illumio-resource-id-flows
      groupId: ip-classification-mmdb-group
      isGroupInstanceIdEnabled: false # Flag for adding the group.instance.id as pod name in env variable
      autoOffsetReset: latest
      requestTimeoutMs: 60000
      maxPollRecords: 4000
      maxPartitionFetchBytes: 4097152
      maxRetries: 5
      maxBackoff: 3s
      assignmentStrategy: "org.apache.kafka.clients.consumer.CooperativeStickyAssignor" # org.apache.kafka.clients.consumer.RangeAssignor # org.apache.kafka.clients.consumer.RoundRobinAssignor # org.apache.kafka.clients.consumer.CooperativeStickyAssignor
      maxPollIntervalMs: 120000    # Reduced from 300s to 120s (2 minutes) for faster rebalancing
      sessionTimeoutMs: 45000      # Reduced from 300s to 45s for faster failure detection
      heartbeatIntervalMs: 3000    # Keep at 3s (should be 1/3 of session timeout)
      # Additional rebalancing configurations
      monitorIntervalInMs: 30000   # 30 seconds monitoring interval
      noMessagesInMs: 180000       # 3 minutes timeout for no messages (reduced from 6 minutes)
      commitRetryIntervalMs: 500   # 500ms between commit retries during rebalancing
    kafkaSenderConfig:
      sinkTopic: illumio-ip-classification-flows
      maxRequestSize: 1000000
      requestTimeoutMs: 60000
      metadataMaxIdleMs: 180000
      deliveryTimeoutMs: 300000
      maxRetries: 5
      maxBackoff: 3s
      lingerMs: 10
      bufferMemoryMb: 48
      batchSizeKb: 64
      numProducers: 1
    grpcConfig:
      timeout: 1s
      maxRetries: 3
      initialBackoff: 100ms
      maxBackoff: 1s
    backpressureConfig:
      enabled: false
      maxSize: 400
      maxTime: 500ms
      lowTide: 60
      highTide: 600
  blueMmdbClient:
    target: "localhost"
    port: 7900
  circuitBreaker:
    enabled: false
    slidingWindowSize: 100
    failureRateThreshold: 50.0
    waitDurationInOpenState: 60s
    permittedNumberOfCallsInHalfOpenState: 10
    slidingWindowType: COUNT_BASED
    slowCallDurationThreshold: 5s
    slowCallRateThreshold: 50.0
  scheduler:
    maxThreadPoolQueueSize: 1200
    keepAliveTimeSeconds: 60
    name: "ipclassification_scheduler"
    maxThreadPoolSize: 20
    grpcMaxThreadPoolQueueSize: 2000
    grpcKeepAliveTimeSeconds: 60
    grpcName: "grpc_scheduler_new"
    grpcMaxThreadPoolSize: 24
jvmOptions: "-XX:MaxRAMPercentage=70.0 -XX:+CrashOnOutOfMemoryError"
