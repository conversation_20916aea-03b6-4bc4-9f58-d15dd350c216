# Kafka Consumer Rebalancing Issue Fix

## Problem Summary

Your IpClassificationMmdbPipeline was experiencing:
1. **RebalanceInProgressException** causing commit failures with 53 retries remaining
2. **Uneven partition distribution** (one pod getting 14 partitions, others getting 7)
3. **Aggressive consumer recreation** during natural rebalancing processes

## Root Cause Analysis

### Primary Issues:
1. **Hidden Consumer Recreation Path**: `RebalanceInProgressException` was triggering consumer recreation through the **retry filter mechanism** (lines 466-486), bypassing the main `recreateConsumerAndRestart()` method
2. **Incorrect Error Classification**: `RebalanceInProgressException` was being treated as a timeout/network error in multiple places
3. **Suboptimal Timeouts**: Session timeout (300s) and max poll interval (300s) were too high, delaying rebalancing completion
4. **Consumer Recreation During Rebalancing**: The pipeline was recreating consumers when it should have allowed natural rebalancing to complete

### The Hidden Recreation Path:
- **RebalanceInProgressException** occurs during normal rebalancing
- **Retry filter** (lines 466-486) calls `isTimeoutOrNetworkError(throwable)`
- **Before fix**: `RebalanceInProgressException` was classified as timeout/network error
- **Retry filter** calls `recreateConsumerAndRestart("retry_filter_timeout_network")` asynchronously
- **This bypasses** the main recreation logging and `isRecreating` flag checks
- **Result**: Consumer gets recreated without the expected log messages, but `startInternal()` still logs "IpClassificationMmdbPipeline started for receiver."

### Configuration Problems:
- `maxPollIntervalMs: 300000` (5 minutes) - Too high
- `sessionTimeoutMs: 300000` (5 minutes) - Too high  
- `noMessagesInMs: 360000` (6 minutes) - Too high
- Missing rebalance-specific timeout configurations

## Solution Implemented

### 1. Configuration Improvements

**Updated Helm Values (`services/ip-classification-mmdb/helm/values.yaml`):**
```yaml
maxPollIntervalMs: 120000    # Reduced from 300s to 120s (2 minutes)
sessionTimeoutMs: 45000      # Reduced from 300s to 45s for faster failure detection
heartbeatIntervalMs: 3000    # Keep at 3s (should be 1/3 of session timeout)
monitorIntervalInMs: 30000   # 30 seconds monitoring interval
noMessagesInMs: 180000       # 3 minutes timeout (reduced from 6 minutes)
rebalanceTimeoutMs: 60000    # 60 seconds for rebalance operations
commitRetryIntervalMs: 500   # 500ms between commit retries during rebalancing
```

**Enhanced Consumer Properties (`IpClassificationMmdbConfig.java`):**
- Added `REBALANCE_TIMEOUT_MS_CONFIG` for better rebalance handling
- Added `reactor.kafka.commit-retry-interval` for configurable commit retry timing
- Added `ENABLE_AUTO_COMMIT_CONFIG: false` for manual commit control
- Added `ISOLATION_LEVEL_CONFIG: read_committed` for consistency

### 2. RebalanceInProgressException Handling

**New Method Added:**
```java
private boolean isRebalanceInProgressError(Throwable error) {
    // Specifically detects RebalanceInProgressException and related messages
    // Returns true for rebalancing errors that should NOT trigger consumer recreation
}
```

**Updated Error Classification:**
```java
private boolean isTimeoutOrNetworkError(Throwable error) {
    // First check if it's a rebalance error - if so, don't treat as timeout/network error
    if (isRebalanceInProgressError(error)) {
        return false;
    }
    // ... rest of timeout/network error detection
}
```

### 3. Improved Error Handling Flow

**Receiver Level:**
- **Rebalance errors**: Log info message, allow natural completion, do NOT recreate consumer
- **LeaveGroup errors**: Immediate consumer recreation (as before)
- **Timeout/Network errors**: Consumer recreation with delay (as before)

**Retry Logic (CRITICAL FIX):**
- **Receiver Retry Filter**: Added rebalance error detection to prevent consumer recreation in retry filter
- **Sender Retry Filter**: Added rebalance error detection to allow proper retry with backoff
- **Rebalance errors**: Allow retry with backoff instead of consumer recreation
- **Other errors**: Maintain existing retry behavior

**Key Fix in Retry Filters:**
```java
// In receiver retry filter (lines 466-492)
if (isRebalanceInProgressError(throwable)) {
    log.info("Rebalance in progress detected in retry filter, will retry with backoff: {}",
            throwable.getMessage());
    return true; // Allow retry for rebalance errors - DO NOT recreate consumer
}

// In sender retry filter (lines 530-539)
if (isRebalanceInProgressError(throwable)) {
    log.info("Rebalance in progress detected in sender retry filter, will retry with backoff: {}",
            throwable.getMessage());
    return true;
}
```

**Sender Level:**
- **Rebalance errors**: Allow retry mechanism to handle with backoff
- **Other errors**: Maintain existing behavior

## Expected Improvements

### 1. Stable Partition Assignment
- Faster rebalancing due to reduced timeouts (45s session timeout vs 300s)
- No consumer recreation during natural rebalancing
- More even partition distribution across 8 pods

### 2. Reduced RebalanceInProgressException Impact
- Rebalance errors will be retried with backoff instead of triggering recreation
- Commit retries will have shorter intervals (500ms vs default)
- Better handling of commit failures during rebalancing

### 3. Improved Monitoring
- Reduced no-messages timeout (3 minutes vs 6 minutes) for faster issue detection
- Better logging to distinguish between rebalance and actual network issues

## Testing Recommendations

### 1. Deployment Testing
```bash
# Deploy the updated configuration
helm upgrade ip-classification-mmdb ./helm -f values.yaml

# Monitor logs for rebalancing behavior
kubectl logs -f deployment/ip-classification-mmdb --tail=100
```

### 2. Monitoring Points
- **Partition Assignment**: Check that partitions are evenly distributed (8 partitions per pod)
- **Rebalancing Logs**: Look for "Rebalance in progress detected, allowing natural completion"
- **Consumer Recreation**: Should see fewer recreation events during normal operation
- **Commit Success**: Monitor for successful commits after rebalancing completes

### 3. Key Log Messages to Watch
- ✅ `"Rebalance in progress detected, allowing natural rebalancing to complete"`
- ✅ `"Rebalance in progress detected in retry filter, will retry with backoff"`
- ✅ `"Rebalance in progress detected in sender retry filter, will retry with backoff"`
- ✅ `"Assigned partitions count: 8, details: [...]"`
- ❌ `"Recreating Kafka consumer due to rebalance issues"` (should not occur for rebalance errors)
- ❌ `"Timeout/network error detected in retry filter, recreating consumer"` (should not occur for rebalance errors)

### 4. Performance Metrics
- **Consumer Lag**: Should remain stable during rebalancing
- **Message Processing Rate**: Should recover quickly after rebalancing
- **Error Rate**: Should see reduction in RebalanceInProgressException errors

## Rollback Plan

If issues occur, you can quickly rollback by reverting these key changes:
1. Restore original timeout values in `values.yaml`
2. Remove the `isRebalanceInProgressError()` method
3. Restore original `isTimeoutOrNetworkError()` logic

The changes are backward compatible and focused on error handling improvements.
